import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import { Label } from '../ui/label';
import { useAuth } from '../../contexts/AuthContext';
import { UserService } from '../../services/database';
import {
  User,
  Camera,
  Edit,
  Save,
  MapPin,
  Phone,
  Mail,
  Star,
  Truck,
  Clock,
  DollarSign,
  Loader,
  Wifi,
  WifiOff,
  CheckCircle,
  TrendingUp,
  Award,
  Navigation
} from 'lucide-react';

export const DriverProfile: React.FC = () => {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [profileData, setProfileData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    licenseNumber: '',
    vehicleType: '',
    vehiclePlate: '',
    emergencyContact: '',
    profilePhoto: '',
    dateJoined: '',
    totalDeliveries: 0,
    averageRating: 0,
    totalEarnings: 0,
    completionRate: 0,
    isActive: false
  });

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Update last update time when user data changes
  useEffect(() => {
    if (user) {
      setLastUpdate(new Date());
    }
  }, [user]);

  // Load user data on component mount
  useEffect(() => {
    loadUserProfile();
  }, [user]);

  const loadUserProfile = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      console.log('🔄 Loading driver profile for:', user.id);

      const userData = await UserService.getById(user.id);
      if (userData) {
        setProfileData({
          name: userData.name || '',
          email: userData.email || '',
          phone: userData.phone || '',
          address: userData.address || '',
          licenseNumber: userData.licenseNumber || '',
          vehicleType: userData.vehicleType || '',
          vehiclePlate: userData.vehiclePlate || '',
          emergencyContact: userData.emergencyContact || '',
          profilePhoto: userData.profilePhoto || '',
          dateJoined: userData.createdAt ? new Date(userData.createdAt.toDate()).toLocaleDateString() : '',
          totalDeliveries: userData.totalDeliveries || 0,
          averageRating: userData.averageRating || 0,
          totalEarnings: userData.totalEarnings || 0,
          completionRate: userData.completionRate || 0,
          isActive: userData.isActive || false
        });
        console.log('✅ Driver profile loaded:', userData);
      }
    } catch (error) {
      console.error('❌ Error loading driver profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    if (!user?.id) return;

    try {
      setSaving(true);
      console.log('🔄 Saving driver profile...');

      const updateData = {
        name: profileData.name,
        phone: profileData.phone,
        address: profileData.address,
        licenseNumber: profileData.licenseNumber,
        vehicleType: profileData.vehicleType,
        vehiclePlate: profileData.vehiclePlate,
        emergencyContact: profileData.emergencyContact,
        updatedAt: new Date()
      };

      await UserService.updateUser(user.id, updateData);
      console.log('✅ Driver profile saved successfully');
      setIsEditing(false);
    } catch (error) {
      console.error('❌ Error saving driver profile:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <Loader className="h-8 w-8 animate-spin text-green-600" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Driver Profile</h1>
          <p className="text-gray-600">Manage your driver information and delivery settings</p>
        </div>
        <div className="flex items-center gap-3">
          {/* Real-time status indicator */}
          <div className="flex items-center gap-2 bg-white shadow-sm rounded-lg px-3 py-2 border">
            {isOnline ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <span className="text-xs text-gray-600">
              {isOnline ? 'Live' : 'Offline'}
            </span>
            <CheckCircle className="h-3 w-3 text-green-500" />
          </div>
          <Button
            onClick={() => isEditing ? handleSaveProfile() : setIsEditing(true)}
            disabled={saving || loading}
            className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
          >
            {saving ? (
              <>
                <Loader className="h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : isEditing ? (
              <>
                <Save className="h-4 w-4" />
                Save Changes
              </>
            ) : (
              <>
                <Edit className="h-4 w-4" />
                Edit Profile
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Driver Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Truck className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{profileData.totalDeliveries}</p>
                <p className="text-sm text-gray-600">Total Deliveries</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{profileData.averageRating.toFixed(1)}</p>
                <p className="text-sm text-gray-600">Average Rating</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">${profileData.totalEarnings.toLocaleString()}</p>
                <p className="text-sm text-gray-600">Total Earnings</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Award className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{profileData.completionRate}%</p>
                <p className="text-sm text-gray-600">Completion Rate</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Vehicle Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Vehicle Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="vehicleType">Vehicle Type</Label>
              <Input
                id="vehicleType"
                value={profileData.vehicleType}
                onChange={(e) => setProfileData({ ...profileData, vehicleType: e.target.value })}
                disabled={!isEditing}
                className="mt-1"
                placeholder="e.g., Motorcycle, Car, Bicycle"
              />
            </div>
            <div>
              <Label htmlFor="vehiclePlate">License Plate</Label>
              <Input
                id="vehiclePlate"
                value={profileData.vehiclePlate}
                onChange={(e) => setProfileData({ ...profileData, vehiclePlate: e.target.value })}
                disabled={!isEditing}
                className="mt-1"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="licenseNumber">Driver's License Number</Label>
            <Input
              id="licenseNumber"
              value={profileData.licenseNumber}
              onChange={(e) => setProfileData({ ...profileData, licenseNumber: e.target.value })}
              disabled={!isEditing}
              className="mt-1"
            />
          </div>
        </CardContent>
      </Card>

      {/* Personal Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Personal Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={profileData.name}
                onChange={(e) => setProfileData({ ...profileData, name: e.target.value })}
                disabled={!isEditing}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={profileData.phone}
                onChange={(e) => setProfileData({ ...profileData, phone: e.target.value })}
                disabled={!isEditing}
                className="mt-1"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                value={profileData.email}
                disabled
                className="mt-1 bg-gray-50"
              />
            </div>
            <div>
              <Label htmlFor="emergencyContact">Emergency Contact</Label>
              <Input
                id="emergencyContact"
                value={profileData.emergencyContact}
                onChange={(e) => setProfileData({ ...profileData, emergencyContact: e.target.value })}
                disabled={!isEditing}
                className="mt-1"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="address">Home Address</Label>
            <Input
              id="address"
              value={profileData.address}
              onChange={(e) => setProfileData({ ...profileData, address: e.target.value })}
              disabled={!isEditing}
              className="mt-1"
            />
          </div>

          <div className="flex items-center gap-4 pt-4 border-t">
            <Badge className={`${profileData.isActive ? 'bg-green-600' : 'bg-gray-600'} text-white`}>
              {profileData.isActive ? 'Active Driver' : 'Inactive'}
            </Badge>
            <span className="text-sm text-gray-600">
              Member since {profileData.dateJoined}
            </span>
            <span className="text-xs text-gray-500">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
