import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  onSnapshot, 
  query, 
  where, 
  orderBy,
  Timestamp 
} from 'firebase/firestore';
import { db } from '../config/firebase';

export interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  order: number;
  roles: string[];
  isActive: boolean;
  badge?: string;
  children?: NavigationItem[];
  route?: string;
  component?: string;
}

export interface DashboardContent {
  id: string;
  role: string;
  title: string;
  description: string;
  widgets: DashboardWidget[];
  layout: string;
  isActive: boolean;
  createdAt: any;
  updatedAt: any;
}

export interface DashboardWidget {
  id: string;
  type: string;
  title: string;
  data: any;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  isVisible: boolean;
}

export interface UIConfiguration {
  id: string;
  role: string;
  theme: {
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
    backgroundColor: string;
  };
  branding: {
    logo: string;
    title: string;
    subtitle: string;
  };
  features: {
    [key: string]: boolean;
  };
  isActive: boolean;
  createdAt: any;
  updatedAt: any;
}

export class MenuService {
  // Navigation Menu Management
  static async getNavigationMenus(role?: string): Promise<NavigationItem[]> {
    try {
      console.log('🔄 Fetching navigation menus for role:', role);
      
      const menusRef = collection(db, 'navigationMenus');
      let q = query(menusRef, orderBy('order', 'asc'));
      
      if (role) {
        q = query(menusRef, where('roles', 'array-contains', role), orderBy('order', 'asc'));
      }
      
      const snapshot = await getDocs(q);
      const menus = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as NavigationItem[];
      
      console.log('✅ Navigation menus loaded:', menus);
      return menus.filter(menu => menu.isActive);
    } catch (error) {
      console.error('❌ Error fetching navigation menus:', error);
      throw error;
    }
  }

  static async createNavigationMenu(menuData: Omit<NavigationItem, 'id'>): Promise<string> {
    try {
      console.log('🔄 Creating navigation menu:', menuData);
      
      const menusRef = collection(db, 'navigationMenus');
      const docRef = doc(menusRef);
      
      const menuWithTimestamp = {
        ...menuData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };
      
      await setDoc(docRef, menuWithTimestamp);
      console.log('✅ Navigation menu created with ID:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('❌ Error creating navigation menu:', error);
      throw error;
    }
  }

  static async updateNavigationMenu(id: string, updates: Partial<NavigationItem>): Promise<void> {
    try {
      console.log('🔄 Updating navigation menu:', id, updates);
      
      const menuRef = doc(db, 'navigationMenus', id);
      const updateData = {
        ...updates,
        updatedAt: Timestamp.now()
      };
      
      await updateDoc(menuRef, updateData);
      console.log('✅ Navigation menu updated successfully');
    } catch (error) {
      console.error('❌ Error updating navigation menu:', error);
      throw error;
    }
  }

  static async deleteNavigationMenu(id: string): Promise<void> {
    try {
      console.log('🔄 Deleting navigation menu:', id);
      
      const menuRef = doc(db, 'navigationMenus', id);
      await deleteDoc(menuRef);
      
      console.log('✅ Navigation menu deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting navigation menu:', error);
      throw error;
    }
  }

  // Real-time navigation menu subscription
  static subscribeToNavigationMenus(
    callback: (menus: NavigationItem[]) => void,
    role?: string
  ): () => void {
    console.log('🔄 Setting up real-time navigation menu subscription for role:', role);
    
    const menusRef = collection(db, 'navigationMenus');
    let q = query(menusRef, orderBy('order', 'asc'));
    
    if (role) {
      q = query(menusRef, where('roles', 'array-contains', role), orderBy('order', 'asc'));
    }
    
    return onSnapshot(q, (snapshot) => {
      const menus = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as NavigationItem[];
      
      const activeMenus = menus.filter(menu => menu.isActive);
      console.log('🔄 Real-time navigation menus update:', activeMenus);
      callback(activeMenus);
    }, (error) => {
      console.error('❌ Error in navigation menu subscription:', error);
    });
  }

  // Dashboard Content Management
  static async getDashboardContent(role: string): Promise<DashboardContent | null> {
    try {
      console.log('🔄 Fetching dashboard content for role:', role);
      
      const contentRef = doc(db, 'dashboardContent', role);
      const snapshot = await getDoc(contentRef);
      
      if (snapshot.exists()) {
        const content = { id: snapshot.id, ...snapshot.data() } as DashboardContent;
        console.log('✅ Dashboard content loaded:', content);
        return content;
      }
      
      console.log('⚠️ No dashboard content found for role:', role);
      return null;
    } catch (error) {
      console.error('❌ Error fetching dashboard content:', error);
      throw error;
    }
  }

  static async updateDashboardContent(role: string, content: Partial<DashboardContent>): Promise<void> {
    try {
      console.log('🔄 Updating dashboard content for role:', role, content);
      
      const contentRef = doc(db, 'dashboardContent', role);
      const updateData = {
        ...content,
        role,
        updatedAt: Timestamp.now()
      };
      
      await setDoc(contentRef, updateData, { merge: true });
      console.log('✅ Dashboard content updated successfully');
    } catch (error) {
      console.error('❌ Error updating dashboard content:', error);
      throw error;
    }
  }

  // Real-time dashboard content subscription
  static subscribeToDashboardContent(
    role: string,
    callback: (content: DashboardContent | null) => void
  ): () => void {
    console.log('🔄 Setting up real-time dashboard content subscription for role:', role);
    
    const contentRef = doc(db, 'dashboardContent', role);
    
    return onSnapshot(contentRef, (snapshot) => {
      if (snapshot.exists()) {
        const content = { id: snapshot.id, ...snapshot.data() } as DashboardContent;
        console.log('🔄 Real-time dashboard content update:', content);
        callback(content);
      } else {
        console.log('⚠️ Dashboard content document does not exist for role:', role);
        callback(null);
      }
    }, (error) => {
      console.error('❌ Error in dashboard content subscription:', error);
    });
  }

  // UI Configuration Management
  static async getUIConfiguration(role: string): Promise<UIConfiguration | null> {
    try {
      console.log('🔄 Fetching UI configuration for role:', role);
      
      const configRef = doc(db, 'uiConfigurations', role);
      const snapshot = await getDoc(configRef);
      
      if (snapshot.exists()) {
        const config = { id: snapshot.id, ...snapshot.data() } as UIConfiguration;
        console.log('✅ UI configuration loaded:', config);
        return config;
      }
      
      console.log('⚠️ No UI configuration found for role:', role);
      return null;
    } catch (error) {
      console.error('❌ Error fetching UI configuration:', error);
      throw error;
    }
  }

  static async updateUIConfiguration(role: string, config: Partial<UIConfiguration>): Promise<void> {
    try {
      console.log('🔄 Updating UI configuration for role:', role, config);
      
      const configRef = doc(db, 'uiConfigurations', role);
      const updateData = {
        ...config,
        role,
        updatedAt: Timestamp.now()
      };
      
      await setDoc(configRef, updateData, { merge: true });
      console.log('✅ UI configuration updated successfully');
    } catch (error) {
      console.error('❌ Error updating UI configuration:', error);
      throw error;
    }
  }

  // Real-time UI configuration subscription
  static subscribeToUIConfiguration(
    role: string,
    callback: (config: UIConfiguration | null) => void
  ): () => void {
    console.log('🔄 Setting up real-time UI configuration subscription for role:', role);
    
    const configRef = doc(db, 'uiConfigurations', role);
    
    return onSnapshot(configRef, (snapshot) => {
      if (snapshot.exists()) {
        const config = { id: snapshot.id, ...snapshot.data() } as UIConfiguration;
        console.log('🔄 Real-time UI configuration update:', config);
        callback(config);
      } else {
        console.log('⚠️ UI configuration document does not exist for role:', role);
        callback(null);
      }
    }, (error) => {
      console.error('❌ Error in UI configuration subscription:', error);
    });
  }
}
