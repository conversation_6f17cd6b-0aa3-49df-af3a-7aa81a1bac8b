import { useState, useEffect, useRef } from 'react';
import { MenuService, NavigationItem, DashboardContent, UIConfiguration } from '../services/menuService';
import { useAuth } from '../contexts/AuthContext';

export interface DynamicMenuState {
  navigationItems: NavigationItem[];
  dashboardContent: DashboardContent | null;
  uiConfiguration: UIConfiguration | null;
  isLoading: boolean;
  error: string | null;
}

export const useDynamicMenu = () => {
  const { user } = useAuth();
  const [state, setState] = useState<DynamicMenuState>({
    navigationItems: [],
    dashboardContent: null,
    uiConfiguration: null,
    isLoading: true,
    error: null
  });

  const unsubscribeRefs = useRef<{
    navigation?: () => void;
    dashboard?: () => void;
    uiConfig?: () => void;
  }>({});

  // Clean up all subscriptions
  const cleanupSubscriptions = () => {
    Object.values(unsubscribeRefs.current).forEach(unsubscribe => {
      if (unsubscribe) unsubscribe();
    });
    unsubscribeRefs.current = {};
  };

  // Set up real-time subscriptions for user role
  useEffect(() => {
    if (!user?.role) {
      setState(prev => ({ ...prev, isLoading: false }));
      return;
    }

    console.log('🔄 Setting up dynamic menu subscriptions for role:', user.role);
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Subscribe to navigation menus
      unsubscribeRefs.current.navigation = MenuService.subscribeToNavigationMenus(
        (navigationItems) => {
          setState(prev => ({ ...prev, navigationItems }));
        },
        user.role
      );

      // Subscribe to dashboard content
      unsubscribeRefs.current.dashboard = MenuService.subscribeToDashboardContent(
        user.role,
        (dashboardContent) => {
          setState(prev => ({ ...prev, dashboardContent }));
        }
      );

      // Subscribe to UI configuration
      unsubscribeRefs.current.uiConfig = MenuService.subscribeToUIConfiguration(
        user.role,
        (uiConfiguration) => {
          setState(prev => ({ ...prev, uiConfiguration, isLoading: false }));
        }
      );

    } catch (error) {
      console.error('❌ Error setting up dynamic menu subscriptions:', error);
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Unknown error',
        isLoading: false 
      }));
    }

    // Cleanup function
    return cleanupSubscriptions;
  }, [user?.role]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanupSubscriptions;
  }, []);

  return state;
};

// Hook for managing navigation items specifically
export const useNavigationItems = () => {
  const { user } = useAuth();
  const [navigationItems, setNavigationItems] = useState<NavigationItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    if (!user?.role) {
      setIsLoading(false);
      return;
    }

    console.log('🔄 Setting up navigation items subscription for role:', user.role);
    setIsLoading(true);
    setError(null);

    try {
      unsubscribeRef.current = MenuService.subscribeToNavigationMenus(
        (items) => {
          setNavigationItems(items);
          setIsLoading(false);
        },
        user.role
      );
    } catch (err) {
      console.error('❌ Error setting up navigation subscription:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setIsLoading(false);
    }

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [user?.role]);

  return { navigationItems, isLoading, error };
};

// Hook for managing dashboard content specifically
export const useDashboardContent = () => {
  const { user } = useAuth();
  const [dashboardContent, setDashboardContent] = useState<DashboardContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    if (!user?.role) {
      setIsLoading(false);
      return;
    }

    console.log('🔄 Setting up dashboard content subscription for role:', user.role);
    setIsLoading(true);
    setError(null);

    try {
      unsubscribeRef.current = MenuService.subscribeToDashboardContent(
        user.role,
        (content) => {
          setDashboardContent(content);
          setIsLoading(false);
        }
      );
    } catch (err) {
      console.error('❌ Error setting up dashboard content subscription:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setIsLoading(false);
    }

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [user?.role]);

  return { dashboardContent, isLoading, error };
};

// Hook for managing UI configuration specifically
export const useUIConfiguration = () => {
  const { user } = useAuth();
  const [uiConfiguration, setUIConfiguration] = useState<UIConfiguration | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    if (!user?.role) {
      setIsLoading(false);
      return;
    }

    console.log('🔄 Setting up UI configuration subscription for role:', user.role);
    setIsLoading(true);
    setError(null);

    try {
      unsubscribeRef.current = MenuService.subscribeToUIConfiguration(
        user.role,
        (config) => {
          setUIConfiguration(config);
          setIsLoading(false);
        }
      );
    } catch (err) {
      console.error('❌ Error setting up UI configuration subscription:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setIsLoading(false);
    }

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [user?.role]);

  return { uiConfiguration, isLoading, error };
};
