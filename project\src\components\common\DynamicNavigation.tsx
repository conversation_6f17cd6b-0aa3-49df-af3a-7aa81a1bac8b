import React from 'react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { useNavigationItems } from '../../hooks/useDynamicMenu';
import { 
  LayoutDashboard, 
  Users, 
  UserCheck, 
  Store, 
  Truck, 
  BarChart3, 
  Shield, 
  Settings,
  Edit,
  Home,
  ShoppingBag,
  Heart,
  UserPlus,
  User,
  ChefHat,
  DollarSign
} from 'lucide-react';

// Icon mapping for dynamic icons
const iconMap: { [key: string]: React.ComponentType<any> } = {
  LayoutDashboard,
  Users,
  UserCheck,
  Store,
  Truck,
  BarChart3,
  Shield,
  Settings,
  Edit,
  Home,
  ShoppingBag,
  Heart,
  UserPlus,
  User,
  ChefHat,
  DollarSign
};

interface DynamicNavigationProps {
  currentPage: string;
  onPageChange: (page: string) => void;
  variant?: 'sidebar' | 'horizontal';
  className?: string;
}

export const DynamicNavigation: React.FC<DynamicNavigationProps> = ({
  currentPage,
  onPageChange,
  variant = 'sidebar',
  className = ''
}) => {
  const { navigationItems, isLoading, error } = useNavigationItems();

  if (isLoading) {
    return (
      <div className={`${className} animate-pulse`}>
        <div className="space-y-2">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-10 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    console.error('Navigation error:', error);
    return (
      <div className={`${className} text-red-500 text-sm`}>
        Failed to load navigation
      </div>
    );
  }

  if (variant === 'horizontal') {
    return (
      <nav className={`flex space-x-1 ${className}`}>
        {navigationItems.map((item) => {
          const IconComponent = iconMap[item.icon] || LayoutDashboard;
          return (
            <Button
              key={item.id}
              variant={currentPage === item.route ? "default" : "ghost"}
              size="sm"
              onClick={() => onPageChange(item.route || item.id)}
              className="flex items-center gap-2"
            >
              <IconComponent className="h-4 w-4" />
              {item.label}
              {item.badge && (
                <Badge variant="secondary" className="ml-1">
                  {item.badge}
                </Badge>
              )}
            </Button>
          );
        })}
      </nav>
    );
  }

  return (
    <nav className={`space-y-1 ${className}`}>
      {navigationItems.map((item) => {
        const IconComponent = iconMap[item.icon] || LayoutDashboard;
        return (
          <button
            key={item.id}
            onClick={() => onPageChange(item.route || item.id)}
            className={`
              w-full flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors
              ${currentPage === (item.route || item.id)
                ? 'bg-primary text-primary-foreground'
                : 'text-gray-700 hover:bg-gray-100'
              }
            `}
          >
            <IconComponent className="h-5 w-5" />
            <span className="flex-1 text-left">{item.label}</span>
            {item.badge && (
              <Badge variant="secondary" className="ml-auto">
                {item.badge}
              </Badge>
            )}
          </button>
        );
      })}
    </nav>
  );
};

// Specialized navigation components for different roles
export const AdminNavigation: React.FC<Omit<DynamicNavigationProps, 'variant'>> = (props) => (
  <DynamicNavigation {...props} variant="sidebar" />
);

export const CustomerNavigation: React.FC<Omit<DynamicNavigationProps, 'variant'>> = (props) => (
  <DynamicNavigation {...props} variant="sidebar" />
);

export const RestaurantNavigation: React.FC<Omit<DynamicNavigationProps, 'variant'>> = (props) => (
  <DynamicNavigation {...props} variant="sidebar" />
);

export const DriverNavigation: React.FC<Omit<DynamicNavigationProps, 'variant'>> = (props) => (
  <DynamicNavigation {...props} variant="sidebar" />
);

// Hook for getting navigation items with fallback
export const useNavigationWithFallback = () => {
  const { navigationItems, isLoading, error } = useNavigationItems();

  // Fallback navigation items if dynamic loading fails
  const fallbackItems = [
    { id: 'dashboard', label: 'Dashboard', icon: 'LayoutDashboard', route: 'dashboard' },
    { id: 'profile', label: 'Profile', icon: 'User', route: 'profile' },
    { id: 'settings', label: 'Settings', icon: 'Settings', route: 'settings' }
  ];

  return {
    navigationItems: error ? fallbackItems : navigationItems,
    isLoading,
    error,
    isFallback: !!error
  };
};
