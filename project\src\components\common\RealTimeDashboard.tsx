import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { useDashboardContent, useUIConfiguration } from '../../hooks/useDynamicMenu';
import { useAuth } from '../../contexts/AuthContext';
import { DashboardContent, UIConfiguration } from '../../services/menuService';
import { 
  Palette, 
  Layout, 
  Loader, 
  AlertCircle,
  CheckCircle,
  Wifi,
  WifiOff
} from 'lucide-react';

interface RealTimeDashboardProps {
  children: React.ReactNode;
  className?: string;
}

export const RealTimeDashboard: React.FC<RealTimeDashboardProps> = ({
  children,
  className = ''
}) => {
  const { user } = useAuth();
  const { dashboardContent, isLoading: contentLoading, error: contentError } = useDashboardContent();
  const { uiConfiguration, isLoading: configLoading, error: configError } = useUIConfiguration();
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Update last update time when content changes
  useEffect(() => {
    if (dashboardContent || uiConfiguration) {
      setLastUpdate(new Date());
    }
  }, [dashboardContent, uiConfiguration]);

  // Apply dynamic theme if available
  useEffect(() => {
    if (uiConfiguration?.theme && uiConfiguration.isActive) {
      const root = document.documentElement;
      root.style.setProperty('--primary-color', uiConfiguration.theme.primaryColor);
      root.style.setProperty('--secondary-color', uiConfiguration.theme.secondaryColor);
      root.style.setProperty('--accent-color', uiConfiguration.theme.accentColor);
      root.style.setProperty('--background-color', uiConfiguration.theme.backgroundColor);
    }
  }, [uiConfiguration]);

  const isLoading = contentLoading || configLoading;
  const hasError = contentError || configError;

  return (
    <div className={`relative ${className}`}>
      {/* Real-time status indicator */}
      <div className="fixed top-4 right-4 z-50">
        <div className="flex items-center gap-2 bg-white shadow-lg rounded-lg px-3 py-2 border">
          {isOnline ? (
            <Wifi className="h-4 w-4 text-green-500" />
          ) : (
            <WifiOff className="h-4 w-4 text-red-500" />
          )}
          <span className="text-xs text-gray-600">
            {isOnline ? 'Live' : 'Offline'}
          </span>
          {isLoading && (
            <Loader className="h-3 w-3 animate-spin text-blue-500" />
          )}
          {hasError && (
            <AlertCircle className="h-3 w-3 text-red-500" />
          )}
          {!isLoading && !hasError && isOnline && (
            <CheckCircle className="h-3 w-3 text-green-500" />
          )}
        </div>
      </div>

      {/* Dashboard content with real-time updates */}
      <div className="space-y-6">
        {/* Dynamic dashboard header */}
        {dashboardContent && (
          <DashboardHeader 
            content={dashboardContent}
            uiConfig={uiConfiguration}
            lastUpdate={lastUpdate}
          />
        )}

        {/* Main dashboard content */}
        <div className={`
          ${dashboardContent?.layout === 'grid' ? 'grid grid-cols-12 gap-6' : ''}
          ${dashboardContent?.layout === 'full-width' ? 'w-full' : ''}
          ${dashboardContent?.layout === 'sidebar' ? 'flex gap-6' : ''}
        `}>
          {children}
        </div>

        {/* Real-time widgets if configured */}
        {dashboardContent?.widgets && dashboardContent.widgets.length > 0 && (
          <div className="grid grid-cols-12 gap-6">
            {dashboardContent.widgets
              .filter(widget => widget.isVisible)
              .map((widget) => (
                <DynamicWidget
                  key={widget.id}
                  widget={widget}
                  uiConfig={uiConfiguration}
                />
              ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Dashboard Header Component
interface DashboardHeaderProps {
  content: DashboardContent;
  uiConfig: UIConfiguration | null;
  lastUpdate: Date;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  content,
  uiConfig,
  lastUpdate
}) => {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          {content.title}
        </h1>
        <p className="text-gray-600">
          {content.description}
        </p>
      </div>
      <div className="flex items-center gap-3">
        <Badge variant="outline" className="flex items-center gap-1">
          <Layout className="h-3 w-3" />
          {content.layout}
        </Badge>
        {uiConfig && (
          <Badge variant="outline" className="flex items-center gap-1">
            <Palette className="h-3 w-3" />
            Themed
          </Badge>
        )}
        <div className="text-xs text-gray-500">
          Updated: {lastUpdate.toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};

// Dynamic Widget Component
interface DynamicWidgetProps {
  widget: any;
  uiConfig: UIConfiguration | null;
}

const DynamicWidget: React.FC<DynamicWidgetProps> = ({
  widget,
  uiConfig
}) => {
  const gridColSpan = `col-span-${Math.min(widget.position.width, 12)}`;
  
  return (
    <div className={gridColSpan}>
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">{widget.title}</CardTitle>
        </CardHeader>
        <CardContent>
          <WidgetContent widget={widget} uiConfig={uiConfig} />
        </CardContent>
      </Card>
    </div>
  );
};

// Widget Content Renderer
interface WidgetContentProps {
  widget: any;
  uiConfig: UIConfiguration | null;
}

const WidgetContent: React.FC<WidgetContentProps> = ({
  widget,
  uiConfig
}) => {
  switch (widget.type) {
    case 'stats':
      return (
        <div className="text-center">
          <div className="text-3xl font-bold text-gray-900">
            {widget.data?.value || '0'}
          </div>
          <div className="text-sm text-gray-600">
            {widget.data?.label || widget.title}
          </div>
        </div>
      );
    
    case 'restaurant-list':
      return (
        <div className="space-y-3">
          <div className="text-center text-gray-600">
            Browse restaurants and place orders
          </div>
          <div className="grid grid-cols-2 gap-3">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-gray-100 rounded-lg p-3 text-center">
                <div className="text-sm font-medium">Restaurant {i}</div>
                <div className="text-xs text-gray-500">Available now</div>
              </div>
            ))}
          </div>
        </div>
      );
    
    default:
      return (
        <div className="text-center text-gray-500">
          <div className="text-sm">Widget: {widget.type}</div>
          <div className="text-xs">Real-time updates enabled</div>
        </div>
      );
  }
};
