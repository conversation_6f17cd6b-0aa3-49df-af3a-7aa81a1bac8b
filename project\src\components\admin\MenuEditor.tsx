import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { MenuService, NavigationItem, DashboardContent, UIConfiguration } from '../../services/menuService';
import { useAuth } from '../../contexts/AuthContext';
import {
  Plus,
  Edit,
  Trash2,
  Save,
  Eye,
  EyeOff,
  ArrowUp,
  ArrowDown,
  Palette,
  Layout,
  Settings
} from 'lucide-react';

export const MenuEditor: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('navigation');
  const [navigationItems, setNavigationItems] = useState<NavigationItem[]>([]);
  const [dashboardContent, setDashboardContent] = useState<{ [role: string]: DashboardContent }>({});
  const [uiConfigurations, setUIConfigurations] = useState<{ [role: string]: UIConfiguration }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [editingItem, setEditingItem] = useState<NavigationItem | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  const roles = ['admin', 'customer', 'restaurant_owner', 'delivery_rider'];
  const availableIcons = [
    'LayoutDashboard', 'Users', 'UserCheck', 'Store', 'Truck', 'BarChart3', 
    'Shield', 'Settings', 'Edit', 'Home', 'ShoppingBag', 'Heart', 'UserPlus', 
    'User', 'ChefHat', 'DollarSign'
  ];

  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    try {
      setIsLoading(true);
      
      // Load navigation items
      const navItems = await MenuService.getNavigationMenus();
      setNavigationItems(navItems);

      // Load dashboard content for all roles
      const dashboardData: { [role: string]: DashboardContent } = {};
      for (const role of roles) {
        const content = await MenuService.getDashboardContent(role);
        if (content) {
          dashboardData[role] = content;
        }
      }
      setDashboardContent(dashboardData);

      // Load UI configurations for all roles
      const uiData: { [role: string]: UIConfiguration } = {};
      for (const role of roles) {
        const config = await MenuService.getUIConfiguration(role);
        if (config) {
          uiData[role] = config;
        }
      }
      setUIConfigurations(uiData);

    } catch (error) {
      console.error('Error loading menu data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveNavigationItem = async (item: Omit<NavigationItem, 'id'>) => {
    try {
      setIsSaving(true);
      
      if (editingItem) {
        await MenuService.updateNavigationMenu(editingItem.id, item);
      } else {
        await MenuService.createNavigationMenu(item);
      }
      
      await loadAllData();
      setEditingItem(null);
      setShowAddForm(false);
    } catch (error) {
      console.error('Error saving navigation item:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteNavigationItem = async (id: string) => {
    if (!confirm('Are you sure you want to delete this navigation item?')) return;
    
    try {
      setIsSaving(true);
      await MenuService.deleteNavigationMenu(id);
      await loadAllData();
    } catch (error) {
      console.error('Error deleting navigation item:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdateDashboardContent = async (role: string, content: Partial<DashboardContent>) => {
    try {
      setIsSaving(true);
      await MenuService.updateDashboardContent(role, content);
      await loadAllData();
    } catch (error) {
      console.error('Error updating dashboard content:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdateUIConfiguration = async (role: string, config: Partial<UIConfiguration>) => {
    try {
      setIsSaving(true);
      await MenuService.updateUIConfiguration(role, config);
      await loadAllData();
    } catch (error) {
      console.error('Error updating UI configuration:', error);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Menu Editor</h1>
            <p className="text-gray-600">Manage navigation menus, dashboard content, and UI configurations</p>
          </div>
        </div>
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Menu Editor</h1>
          <p className="text-gray-600">Manage navigation menus, dashboard content, and UI configurations</p>
        </div>
        <Badge variant="outline" className="bg-blue-50 text-blue-700">
          Real-time Updates
        </Badge>
      </div>

      {/* Simple tab navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('navigation')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'navigation'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Layout className="h-4 w-4 inline mr-2" />
            Navigation
          </button>
          <button
            onClick={() => setActiveTab('dashboard')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'dashboard'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Settings className="h-4 w-4 inline mr-2" />
            Dashboard
          </button>
          <button
            onClick={() => setActiveTab('ui-config')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'ui-config'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Palette className="h-4 w-4 inline mr-2" />
            UI Config
          </button>
        </nav>
      </div>

      {activeTab === 'navigation' && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">Navigation Items</h2>
            <Button
              onClick={() => setShowAddForm(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Navigation Item
            </Button>
          </div>

          {showAddForm && (
            <NavigationItemForm
              item={editingItem}
              onSave={handleSaveNavigationItem}
              onCancel={() => {
                setShowAddForm(false);
                setEditingItem(null);
              }}
              isSaving={isSaving}
              availableIcons={availableIcons}
              roles={roles}
            />
          )}

          <div className="grid gap-4">
            {navigationItems.map((item) => (
              <NavigationItemCard
                key={item.id}
                item={item}
                onEdit={(item) => {
                  setEditingItem(item);
                  setShowAddForm(true);
                }}
                onDelete={handleDeleteNavigationItem}
                isSaving={isSaving}
              />
            ))}
          </div>
        </div>
      )}

      {activeTab === 'dashboard' && (
        <div className="space-y-6">
          <h2 className="text-lg font-semibold">Dashboard Content</h2>
          
          <div className="grid gap-6">
            {roles.map((role) => (
              <DashboardContentEditor
                key={role}
                role={role}
                content={dashboardContent[role]}
                onUpdate={(content) => handleUpdateDashboardContent(role, content)}
                isSaving={isSaving}
              />
            ))}
          </div>
        </div>
      )}

      {activeTab === 'ui-config' && (
        <div className="space-y-6">
          <h2 className="text-lg font-semibold">UI Configurations</h2>
          
          <div className="grid gap-6">
            {roles.map((role) => (
              <UIConfigurationEditor
                key={role}
                role={role}
                config={uiConfigurations[role]}
                onUpdate={(config) => handleUpdateUIConfiguration(role, config)}
                isSaving={isSaving}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Navigation Item Form Component
interface NavigationItemFormProps {
  item: NavigationItem | null;
  onSave: (item: Omit<NavigationItem, 'id'>) => void;
  onCancel: () => void;
  isSaving: boolean;
  availableIcons: string[];
  roles: string[];
}

const NavigationItemForm: React.FC<NavigationItemFormProps> = ({
  item,
  onSave,
  onCancel,
  isSaving,
  availableIcons,
  roles
}) => {
  const [formData, setFormData] = useState({
    label: item?.label || '',
    icon: item?.icon || 'LayoutDashboard',
    order: item?.order || 1,
    roles: item?.roles || [],
    isActive: item?.isActive ?? true,
    route: item?.route || '',
    component: item?.component || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{item ? 'Edit Navigation Item' : 'Add Navigation Item'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="label">Label</Label>
              <Input
                id="label"
                value={formData.label}
                onChange={(e) => setFormData({ ...formData, label: e.target.value })}
                required
              />
            </div>
            <div>
              <Label htmlFor="icon">Icon</Label>
              <select
                value={formData.icon}
                onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                {availableIcons.map((icon) => (
                  <option key={icon} value={icon}>{icon}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="route">Route</Label>
              <Input
                id="route"
                value={formData.route}
                onChange={(e) => setFormData({ ...formData, route: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="component">Component</Label>
              <Input
                id="component"
                value={formData.component}
                onChange={(e) => setFormData({ ...formData, component: e.target.value })}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="order">Order</Label>
            <Input
              id="order"
              type="number"
              value={formData.order}
              onChange={(e) => setFormData({ ...formData, order: parseInt(e.target.value) })}
              min="1"
            />
          </div>

          <div>
            <Label>Roles</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {roles.map((role) => (
                <label key={role} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.roles.includes(role)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setFormData({ ...formData, roles: [...formData.roles, role] });
                      } else {
                        setFormData({ ...formData, roles: formData.roles.filter(r => r !== role) });
                      }
                    }}
                  />
                  <span className="capitalize">{role.replace('_', ' ')}</span>
                </label>
              ))}
            </div>
          </div>

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={formData.isActive}
              onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
            />
            <Label>Active</Label>
          </div>

          <div className="flex gap-2">
            <Button type="submit" disabled={isSaving}>
              {isSaving ? 'Saving...' : 'Save'}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

// Navigation Item Card Component
interface NavigationItemCardProps {
  item: NavigationItem;
  onEdit: (item: NavigationItem) => void;
  onDelete: (id: string) => void;
  isSaving: boolean;
}

const NavigationItemCard: React.FC<NavigationItemCardProps> = ({
  item,
  onEdit,
  onDelete,
  isSaving
}) => {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">#{item.order}</span>
              <span className="text-lg font-semibold">{item.label}</span>
              <Badge variant="outline">{item.icon}</Badge>
            </div>
            <div className="flex gap-1">
              {item.roles.map((role) => (
                <Badge key={role} variant="secondary" className="text-xs">
                  {role.replace('_', ' ')}
                </Badge>
              ))}
            </div>
            {!item.isActive && (
              <Badge variant="destructive">Inactive</Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => onEdit(item)}
              disabled={isSaving}
            >
              <Edit className="h-3 w-3" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onDelete(item.id)}
              disabled={isSaving}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
        {item.route && (
          <div className="mt-2 text-sm text-gray-600">
            Route: <code className="bg-gray-100 px-1 rounded">{item.route}</code>
            {item.component && (
              <span className="ml-2">
                Component: <code className="bg-gray-100 px-1 rounded">{item.component}</code>
              </span>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Dashboard Content Editor Component
interface DashboardContentEditorProps {
  role: string;
  content: DashboardContent | undefined;
  onUpdate: (content: Partial<DashboardContent>) => void;
  isSaving: boolean;
}

const DashboardContentEditor: React.FC<DashboardContentEditorProps> = ({
  role,
  content,
  onUpdate,
  isSaving
}) => {
  const [formData, setFormData] = useState({
    title: content?.title || '',
    description: content?.description || '',
    layout: content?.layout || 'grid',
    isActive: content?.isActive ?? true
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate(formData);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="capitalize">{role.replace('_', ' ')} Dashboard</span>
          <Badge variant={content ? 'default' : 'secondary'}>
            {content ? 'Configured' : 'Not Configured'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor={`title-${role}`}>Title</Label>
            <Input
              id={`title-${role}`}
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              placeholder="Dashboard title"
            />
          </div>

          <div>
            <Label htmlFor={`description-${role}`}>Description</Label>
            <textarea
              id={`description-${role}`}
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Dashboard description"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>

          <div>
            <Label htmlFor={`layout-${role}`}>Layout</Label>
            <select
              value={formData.layout}
              onChange={(e) => setFormData({ ...formData, layout: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="grid">Grid Layout</option>
              <option value="full-width">Full Width</option>
              <option value="sidebar">Sidebar Layout</option>
            </select>
          </div>

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={formData.isActive}
              onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
            />
            <Label>Active</Label>
          </div>

          <Button type="submit" disabled={isSaving} className="w-full">
            {isSaving ? 'Updating...' : 'Update Dashboard Content'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

// UI Configuration Editor Component
interface UIConfigurationEditorProps {
  role: string;
  config: UIConfiguration | undefined;
  onUpdate: (config: Partial<UIConfiguration>) => void;
  isSaving: boolean;
}

const UIConfigurationEditor: React.FC<UIConfigurationEditorProps> = ({
  role,
  config,
  onUpdate,
  isSaving
}) => {
  const [formData, setFormData] = useState({
    theme: {
      primaryColor: config?.theme?.primaryColor || '#3b82f6',
      secondaryColor: config?.theme?.secondaryColor || '#1e40af',
      accentColor: config?.theme?.accentColor || '#f59e0b',
      backgroundColor: config?.theme?.backgroundColor || '#ffffff'
    },
    branding: {
      logo: config?.branding?.logo || '/vector---0.svg',
      title: config?.branding?.title || 'Grubz',
      subtitle: config?.branding?.subtitle || 'Portal'
    },
    isActive: config?.isActive ?? true
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate(formData);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="capitalize">{role.replace('_', ' ')} UI Configuration</span>
          <Badge variant={config ? 'default' : 'secondary'}>
            {config ? 'Configured' : 'Not Configured'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <h4 className="text-sm font-medium mb-3">Theme Colors</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor={`primary-${role}`}>Primary Color</Label>
                <div className="flex gap-2">
                  <input
                    id={`primary-${role}`}
                    type="color"
                    value={formData.theme.primaryColor}
                    onChange={(e) => setFormData({
                      ...formData,
                      theme: { ...formData.theme, primaryColor: e.target.value }
                    })}
                    className="w-16 h-10"
                  />
                  <Input
                    value={formData.theme.primaryColor}
                    onChange={(e) => setFormData({
                      ...formData,
                      theme: { ...formData.theme, primaryColor: e.target.value }
                    })}
                    placeholder="#3b82f6"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor={`secondary-${role}`}>Secondary Color</Label>
                <div className="flex gap-2">
                  <input
                    id={`secondary-${role}`}
                    type="color"
                    value={formData.theme.secondaryColor}
                    onChange={(e) => setFormData({
                      ...formData,
                      theme: { ...formData.theme, secondaryColor: e.target.value }
                    })}
                    className="w-16 h-10"
                  />
                  <Input
                    value={formData.theme.secondaryColor}
                    onChange={(e) => setFormData({
                      ...formData,
                      theme: { ...formData.theme, secondaryColor: e.target.value }
                    })}
                    placeholder="#1e40af"
                  />
                </div>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-3">Branding</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor={`title-${role}`}>Title</Label>
                <Input
                  id={`title-${role}`}
                  value={formData.branding.title}
                  onChange={(e) => setFormData({
                    ...formData,
                    branding: { ...formData.branding, title: e.target.value }
                  })}
                  placeholder="Grubz"
                />
              </div>
              <div>
                <Label htmlFor={`subtitle-${role}`}>Subtitle</Label>
                <Input
                  id={`subtitle-${role}`}
                  value={formData.branding.subtitle}
                  onChange={(e) => setFormData({
                    ...formData,
                    branding: { ...formData.branding, subtitle: e.target.value }
                  })}
                  placeholder="Portal"
                />
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={formData.isActive}
              onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
            />
            <Label>Active</Label>
          </div>

          <Button type="submit" disabled={isSaving} className="w-full">
            {isSaving ? 'Updating...' : 'Update UI Configuration'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
