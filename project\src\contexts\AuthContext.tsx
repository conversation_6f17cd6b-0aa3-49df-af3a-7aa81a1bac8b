import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { User as FirebaseUser } from 'firebase/auth';
import { AuthService, UserData } from '../services/auth';
import { UserService } from '../services/database';
import { onSnapshot, doc } from 'firebase/firestore';
import { db } from '../config/firebase';

export type UserRole = 'admin' | 'customer' | 'restaurant_owner' | 'delivery_rider';

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  phone?: string;
  address?: string;
  emailVerified?: boolean;
}

interface AuthContextType {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  login: (email: string, password: string) => Promise<boolean>;
  signup: (email: string, password: string, name: string, role: UserRole, phone?: string, address?: string) => Promise<boolean>;
  registerCustomer: (email: string, password: string, name: string, phone?: string, address?: string) => Promise<boolean>;
  logout: () => void;
  updateProfile: (userData: Partial<User>) => Promise<boolean>;
  resetPassword: (email: string) => Promise<boolean>;
  resendEmailVerification: (email: string, password: string) => Promise<boolean>;
  isLoading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const userProfileUnsubscribeRef = useRef<(() => void) | null>(null);

  // Demo mode check
  const isDemoMode = import.meta.env.VITE_FIREBASE_API_KEY === "demo-api-key" || !import.meta.env.VITE_FIREBASE_API_KEY;

  // Function to set up real-time user profile listening
  const setupUserProfileListener = (userId: string) => {
    // Clean up any existing listener
    if (userProfileUnsubscribeRef.current) {
      userProfileUnsubscribeRef.current();
    }

    console.log('🔄 Setting up real-time user profile listener for:', userId);

    const userDocRef = doc(db, 'users', userId);
    const unsubscribe = onSnapshot(userDocRef, (doc) => {
      if (doc.exists()) {
        const userData = { id: doc.id, ...doc.data() } as User;
        console.log('🔄 Real-time user profile update:', userData);
        setUser(userData);
      } else {
        console.warn('⚠️ User document does not exist:', userId);
      }
    }, (error) => {
      console.error('❌ Error in user profile listener:', error);
    });

    userProfileUnsubscribeRef.current = unsubscribe;
  };

  // Function to clean up user profile listener
  const cleanupUserProfileListener = () => {
    if (userProfileUnsubscribeRef.current) {
      console.log('🧹 Cleaning up user profile listener');
      userProfileUnsubscribeRef.current();
      userProfileUnsubscribeRef.current = null;
    }
  };

  useEffect(() => {
    // Set a timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      console.log('⚠️ Loading timeout reached, forcing loading to false');
      setIsLoading(false);
    }, 10000); // 10 second timeout

    // Listen to Firebase auth state changes
    const unsubscribe = AuthService.onAuthStateChange(async (firebaseUser) => {
      console.log('🔄 Auth state change:', firebaseUser ? `User signed in: ${firebaseUser.email}` : 'User signed out');
      setFirebaseUser(firebaseUser);

      if (firebaseUser) {
        // Get user data first to check role
        let userData;
        try {
          console.log('🔄 Fetching user data from Firestore for UID:', firebaseUser.uid);
          userData = await AuthService.getUserData(firebaseUser.uid);
        } catch (error) {
          console.error('❌ Error fetching user data:', error);
          setIsLoading(false);
          return;
        }

        // Check if email is verified before proceeding (skip for admin users)
        if (!firebaseUser.emailVerified && userData?.role !== 'admin') {
          console.log('❌ User email not verified, signing out');
          await AuthService.logout();
          return;
        }

        // User is signed in and verified (or is admin), process user data
        if (userData) {
          console.log('✅ User data loaded:', userData);
          setUser(userData as User);
          setIsAuthenticated(true);

          // Set up real-time listener for user profile updates
          setupUserProfileListener(firebaseUser.uid);

          console.log('✅ User state updated successfully');
        } else {
          // User exists in Firebase Auth but not in Firestore
          console.error('❌ User data not found in Firestore for:', firebaseUser.email);
          console.log('🔄 Creating fallback user data...');

          // Create a fallback user object for testing
          const fallbackUser: User = {
            id: firebaseUser.uid,
            email: firebaseUser.email || '',
            name: firebaseUser.displayName || 'Customer',
            role: 'customer',
            phone: '',
            address: ''
          };

          console.log('✅ Using fallback user data:', fallbackUser);
          setUser(fallbackUser);
          setIsAuthenticated(true);

          // Set up real-time listener for fallback user too
          setupUserProfileListener(firebaseUser.uid);
        }
      } else {
        // User is signed out
        console.log('👋 User signed out');
        cleanupUserProfileListener();
        setUser(null);
        setIsAuthenticated(false);
      }

      console.log('🔄 Auth state processing complete, setting loading to false');
      clearTimeout(loadingTimeout);
      setIsLoading(false);
    });

    // Create default admin user and test customer on first load
    AuthService.createDefaultAdmin().catch(console.error);
    AuthService.createTestCustomer().catch(console.error);

    // Cleanup subscription and timeout on unmount
    return () => {
      clearTimeout(loadingTimeout);
      cleanupUserProfileListener();
      unsubscribe();
    };
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    console.log('🔄 Starting login for:', email);

    try {
      const userData = await AuthService.login(email, password);
      console.log('✅ Login successful, user data:', userData);
      // User state will be updated automatically by the auth state listener
      setIsLoading(false);
      return true;
    } catch (error) {
      console.error('❌ Login error:', error);
      setIsLoading(false);
      // Re-throw the error so the UI can handle it properly
      throw error;
    }
  };

  const signup = async (
    email: string,
    password: string,
    name: string,
    role: UserRole,
    phone?: string,
    address?: string
  ): Promise<boolean> => {
    setIsLoading(true);

    try {
      const userData = await AuthService.register(email, password, {
        name,
        role,
        phone,
        address
      });
      // User state will be updated automatically by the auth state listener
      setIsLoading(false);
      return true;
    } catch (error) {
      console.error('Signup error:', error);
      setIsLoading(false);
      return false;
    }
  };

  const registerCustomer = async (
    email: string,
    password: string,
    name: string,
    phone?: string,
    address?: string
  ): Promise<boolean> => {
    setIsLoading(true);
    console.log('🔄 Starting customer registration for:', email);

    try {
      const userData = await AuthService.registerWithoutLogin(email, password, {
        name,
        role: 'customer',
        phone,
        address
      });
      console.log('✅ Customer registration successful:', userData);
      // User is signed out after registration, so no auth state change
      setIsLoading(false);
      return true;
    } catch (error) {
      console.error('❌ Customer registration error in AuthContext:', error);
      setIsLoading(false);
      return false;
    }
  };

  const logout = async () => {
    try {
      console.log('🔄 Starting logout process...');
      setIsLoading(true);

      // Clean up real-time listeners
      cleanupUserProfileListener();

      // Clear user state immediately
      setUser(null);
      setIsAuthenticated(false);

      // Sign out from Firebase
      await AuthService.logout();
      console.log('✅ Logout successful');

      // Force redirect to home page
      window.location.href = '/';
    } catch (error) {
      console.error('❌ Logout error:', error);

      // Clean up listeners even if logout fails
      cleanupUserProfileListener();

      // Force logout even if Firebase logout fails
      setUser(null);
      setIsAuthenticated(false);
      setIsLoading(false);

      // Force redirect to home page
      window.location.href = '/';
    }
  };

  const updateProfile = async (userData: Partial<User>): Promise<boolean> => {
    try {
      await AuthService.updateUserProfile(userData);
      // Update local user state
      if (user) {
        setUser({ ...user, ...userData });
      }
      return true;
    } catch (error) {
      console.error('Update profile error:', error);
      return false;
    }
  };

  const resetPassword = async (email: string): Promise<boolean> => {
    try {
      await AuthService.resetPassword(email);
      return true;
    } catch (error) {
      console.error('Reset password error:', error);
      return false;
    }
  };

  const resendEmailVerification = async (email: string, password: string): Promise<boolean> => {
    try {
      await AuthService.resendEmailVerification(email, password);
      return true;
    } catch (error) {
      console.error('Resend email verification error:', error);
      return false;
    }
  };

  const value: AuthContextType = {
    user,
    firebaseUser,
    login,
    signup,
    registerCustomer,
    logout,
    updateProfile,
    resetPassword,
    resendEmailVerification,
    isLoading,
    isAuthenticated,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};