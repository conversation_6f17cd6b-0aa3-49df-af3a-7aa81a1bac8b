import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { useAuth } from '../../contexts/AuthContext';
import { UserService } from '../../services/database';
import {
  User,
  Camera,
  Edit,
  Save,
  MapPin,
  Phone,
  Mail,
  Star,
  Store,
  Clock,
  DollarSign,
  Loader,
  Wifi,
  WifiOff,
  CheckCircle,
  Users,
  TrendingUp
} from 'lucide-react';

export const RestaurantProfile: React.FC = () => {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [profileData, setProfileData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    restaurantName: '',
    cuisine: '',
    description: '',
    operatingHours: '',
    profilePhoto: '',
    dateJoined: '',
    totalOrders: 0,
    averageRating: 0,
    totalRevenue: 0,
    activeMenuItems: 0
  });

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Update last update time when user data changes
  useEffect(() => {
    if (user) {
      setLastUpdate(new Date());
    }
  }, [user]);

  // Load user data on component mount
  useEffect(() => {
    loadUserProfile();
  }, [user]);

  const loadUserProfile = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      console.log('🔄 Loading restaurant profile for:', user.id);

      const userData = await UserService.getById(user.id);
      if (userData) {
        setProfileData({
          name: userData.name || '',
          email: userData.email || '',
          phone: userData.phone || '',
          address: userData.address || '',
          restaurantName: userData.restaurantName || '',
          cuisine: userData.cuisine || '',
          description: userData.description || '',
          operatingHours: userData.operatingHours || '',
          profilePhoto: userData.profilePhoto || '',
          dateJoined: userData.createdAt ? new Date(userData.createdAt.toDate()).toLocaleDateString() : '',
          totalOrders: userData.totalOrders || 0,
          averageRating: userData.averageRating || 0,
          totalRevenue: userData.totalRevenue || 0,
          activeMenuItems: userData.activeMenuItems || 0
        });
        console.log('✅ Restaurant profile loaded:', userData);
      }
    } catch (error) {
      console.error('❌ Error loading restaurant profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    if (!user?.id) return;

    try {
      setSaving(true);
      console.log('🔄 Saving restaurant profile...');

      const updateData = {
        name: profileData.name,
        phone: profileData.phone,
        address: profileData.address,
        restaurantName: profileData.restaurantName,
        cuisine: profileData.cuisine,
        description: profileData.description,
        operatingHours: profileData.operatingHours,
        updatedAt: new Date()
      };

      await UserService.updateUser(user.id, updateData);
      console.log('✅ Restaurant profile saved successfully');
      setIsEditing(false);
    } catch (error) {
      console.error('❌ Error saving restaurant profile:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <Loader className="h-8 w-8 animate-spin text-[#704ce5]" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Restaurant Profile</h1>
          <p className="text-gray-600">Manage your restaurant information and settings</p>
        </div>
        <div className="flex items-center gap-3">
          {/* Real-time status indicator */}
          <div className="flex items-center gap-2 bg-white shadow-sm rounded-lg px-3 py-2 border">
            {isOnline ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <span className="text-xs text-gray-600">
              {isOnline ? 'Live' : 'Offline'}
            </span>
            <CheckCircle className="h-3 w-3 text-green-500" />
          </div>
          <Button
            onClick={() => isEditing ? handleSaveProfile() : setIsEditing(true)}
            disabled={saving || loading}
            className="bg-[#704ce5] hover:bg-[#5a3bc4] flex items-center gap-2"
          >
            {saving ? (
              <>
                <Loader className="h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : isEditing ? (
              <>
                <Save className="h-4 w-4" />
                Save Changes
              </>
            ) : (
              <>
                <Edit className="h-4 w-4" />
                Edit Profile
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Restaurant Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{profileData.totalOrders}</p>
                <p className="text-sm text-gray-600">Total Orders</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{profileData.averageRating.toFixed(1)}</p>
                <p className="text-sm text-gray-600">Average Rating</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">${profileData.totalRevenue.toLocaleString()}</p>
                <p className="text-sm text-gray-600">Total Revenue</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Store className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{profileData.activeMenuItems}</p>
                <p className="text-sm text-gray-600">Menu Items</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Profile Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Store className="h-5 w-5" />
            Restaurant Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="restaurantName">Restaurant Name</Label>
              <Input
                id="restaurantName"
                value={profileData.restaurantName}
                onChange={(e) => setProfileData({ ...profileData, restaurantName: e.target.value })}
                disabled={!isEditing}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="cuisine">Cuisine Type</Label>
              <Input
                id="cuisine"
                value={profileData.cuisine}
                onChange={(e) => setProfileData({ ...profileData, cuisine: e.target.value })}
                disabled={!isEditing}
                className="mt-1"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description">Restaurant Description</Label>
            <Textarea
              id="description"
              value={profileData.description}
              onChange={(e) => setProfileData({ ...profileData, description: e.target.value })}
              disabled={!isEditing}
              className="mt-1"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="operatingHours">Operating Hours</Label>
              <Input
                id="operatingHours"
                value={profileData.operatingHours}
                onChange={(e) => setProfileData({ ...profileData, operatingHours: e.target.value })}
                disabled={!isEditing}
                className="mt-1"
                placeholder="e.g., Mon-Sun 9AM-10PM"
              />
            </div>
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={profileData.phone}
                onChange={(e) => setProfileData({ ...profileData, phone: e.target.value })}
                disabled={!isEditing}
                className="mt-1"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="address">Restaurant Address</Label>
            <Input
              id="address"
              value={profileData.address}
              onChange={(e) => setProfileData({ ...profileData, address: e.target.value })}
              disabled={!isEditing}
              className="mt-1"
            />
          </div>
        </CardContent>
      </Card>

      {/* Account Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Account Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="name">Owner Name</Label>
              <Input
                id="name"
                value={profileData.name}
                onChange={(e) => setProfileData({ ...profileData, name: e.target.value })}
                disabled={!isEditing}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                value={profileData.email}
                disabled
                className="mt-1 bg-gray-50"
              />
            </div>
          </div>

          <div className="flex items-center gap-4 pt-4 border-t">
            <Badge className="bg-[#704ce5] text-white">Restaurant Owner</Badge>
            <span className="text-sm text-gray-600">
              Member since {profileData.dateJoined}
            </span>
            <span className="text-xs text-gray-500">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
