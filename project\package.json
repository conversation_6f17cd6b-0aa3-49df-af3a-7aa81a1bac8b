{"version": "1.0.0", "source": "./index.html", "type": "module", "name": "anima-project", "description": "A React project automatically generated by <PERSON><PERSON> using the Shadcn UI library", "scripts": {"dev": "vite", "build": "vite build"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.0", "clsx": "2.1.1", "embla-carousel-react": "^8.3.0", "firebase": "^11.10.0", "lucide-react": "^0.453.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "tailwind-merge": "2.5.4"}, "devDependencies": {"@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@vitejs/plugin-react": "4.3.4", "esbuild": "0.24.0", "globals": "15.12.0", "tailwindcss": "3.4.16", "vite": "6.0.4"}, "alias": {"@/*": "./src/components/ui/$1"}}