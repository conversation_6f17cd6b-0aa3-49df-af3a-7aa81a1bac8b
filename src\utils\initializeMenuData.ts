import { MenuService, NavigationItem, DashboardContent, UIConfiguration } from '../services/menuService';

// Default navigation menus for each role
const defaultNavigationMenus: Omit<NavigationItem, 'id'>[] = [
  // Admin Navigation
  {
    label: 'Dashboard',
    icon: 'LayoutDashboard',
    order: 1,
    roles: ['admin'],
    isActive: true,
    route: 'dashboard',
    component: 'AdminDashboardOverview'
  },
  {
    label: 'User Management',
    icon: 'Users',
    order: 2,
    roles: ['admin'],
    isActive: true,
    route: 'users',
    component: 'UserManagement'
  },
  {
    label: 'Partner Requests',
    icon: 'UserCheck',
    order: 3,
    roles: ['admin'],
    isActive: true,
    route: 'partner-requests',
    component: 'PartnerRequestManagement'
  },
  {
    label: 'Restaurants',
    icon: 'Store',
    order: 4,
    roles: ['admin'],
    isActive: true,
    route: 'restaurants',
    component: 'RestaurantManagement'
  },
  {
    label: 'Delivery Management',
    icon: 'Truck',
    order: 5,
    roles: ['admin'],
    isActive: true,
    route: 'deliveries',
    component: 'DeliveryManagement'
  },
  {
    label: 'Analytics',
    icon: 'BarChart3',
    order: 6,
    roles: ['admin'],
    isActive: true,
    route: 'analytics',
    component: 'PlatformAnalytics'
  },
  {
    label: 'Data Manager',
    icon: 'Shield',
    order: 7,
    roles: ['admin'],
    isActive: true,
    route: 'data',
    component: 'DataManager'
  },
  {
    label: 'System Settings',
    icon: 'Settings',
    order: 8,
    roles: ['admin'],
    isActive: true,
    route: 'settings',
    component: 'SystemSettings'
  },
  {
    label: 'Menu Editor',
    icon: 'Edit',
    order: 9,
    roles: ['admin'],
    isActive: true,
    route: 'menu-editor',
    component: 'MenuEditor'
  },

  // Customer Navigation
  {
    label: 'Home',
    icon: 'Home',
    order: 1,
    roles: ['customer'],
    isActive: true,
    route: 'home',
    component: 'CustomerHome'
  },
  {
    label: 'My Orders',
    icon: 'ShoppingBag',
    order: 2,
    roles: ['customer'],
    isActive: true,
    route: 'orders',
    component: 'CustomerOrders'
  },
  {
    label: 'Favorites',
    icon: 'Heart',
    order: 3,
    roles: ['customer'],
    isActive: true,
    route: 'favorites',
    component: 'CustomerFavorites'
  },
  {
    label: 'Partner Applications',
    icon: 'UserPlus',
    order: 4,
    roles: ['customer'],
    isActive: true,
    route: 'partner-apply',
    component: 'PartnerApplications'
  },
  {
    label: 'My Profile',
    icon: 'User',
    order: 5,
    roles: ['customer'],
    isActive: true,
    route: 'profile',
    component: 'CustomerProfile'
  },
  {
    label: 'Settings',
    icon: 'Settings',
    order: 6,
    roles: ['customer'],
    isActive: true,
    route: 'settings',
    component: 'CustomerSettings'
  },

  // Restaurant Navigation
  {
    label: 'Dashboard',
    icon: 'LayoutDashboard',
    order: 1,
    roles: ['restaurant_owner'],
    isActive: true,
    route: 'dashboard',
    component: 'DashboardOverview'
  },
  {
    label: 'Orders',
    icon: 'ShoppingBag',
    order: 2,
    roles: ['restaurant_owner'],
    isActive: true,
    route: 'orders',
    component: 'OrdersManagement'
  },
  {
    label: 'Menu',
    icon: 'ChefHat',
    order: 3,
    roles: ['restaurant_owner'],
    isActive: true,
    route: 'menu',
    component: 'MenuManagement'
  },
  {
    label: 'Analytics',
    icon: 'BarChart3',
    order: 4,
    roles: ['restaurant_owner'],
    isActive: true,
    route: 'analytics',
    component: 'RestaurantAnalytics'
  },
  {
    label: 'Settings',
    icon: 'Settings',
    order: 5,
    roles: ['restaurant_owner'],
    isActive: true,
    route: 'settings',
    component: 'RestaurantSettings'
  },
  {
    label: 'Profile',
    icon: 'User',
    order: 6,
    roles: ['restaurant_owner'],
    isActive: true,
    route: 'profile',
    component: 'RestaurantProfile'
  },

  // Driver Navigation
  {
    label: 'Dashboard',
    icon: 'LayoutDashboard',
    order: 1,
    roles: ['delivery_rider'],
    isActive: true,
    route: 'dashboard',
    component: 'DriverDashboardOverview'
  },
  {
    label: 'Active Deliveries',
    icon: 'Truck',
    order: 2,
    roles: ['delivery_rider'],
    isActive: true,
    route: 'deliveries',
    component: 'DriverActiveDeliveries'
  },
  {
    label: 'Earnings',
    icon: 'DollarSign',
    order: 3,
    roles: ['delivery_rider'],
    isActive: true,
    route: 'earnings',
    component: 'DriverEarnings'
  },
  {
    label: 'Delivery History',
    icon: 'BarChart3',
    order: 4,
    roles: ['delivery_rider'],
    isActive: true,
    route: 'history',
    component: 'DriverHistory'
  },
  {
    label: 'Profile',
    icon: 'User',
    order: 5,
    roles: ['delivery_rider'],
    isActive: true,
    route: 'profile',
    component: 'DriverProfile'
  },
  {
    label: 'Settings',
    icon: 'Settings',
    order: 6,
    roles: ['delivery_rider'],
    isActive: true,
    route: 'settings',
    component: 'DriverSettings'
  }
];

// Default dashboard content for each role
const defaultDashboardContent: { [role: string]: Omit<DashboardContent, 'id'> } = {
  admin: {
    role: 'admin',
    title: 'Admin Dashboard',
    description: 'Manage your food delivery platform',
    widgets: [
      {
        id: 'user-stats',
        type: 'stats',
        title: 'User Statistics',
        data: { metric: 'users' },
        position: { x: 0, y: 0, width: 6, height: 4 },
        isVisible: true
      },
      {
        id: 'order-stats',
        type: 'stats',
        title: 'Order Statistics',
        data: { metric: 'orders' },
        position: { x: 6, y: 0, width: 6, height: 4 },
        isVisible: true
      }
    ],
    layout: 'grid',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  customer: {
    role: 'customer',
    title: 'Grubber Dashboard',
    description: 'Discover amazing food and track your orders',
    widgets: [
      {
        id: 'restaurants',
        type: 'restaurant-list',
        title: 'Browse Restaurants',
        data: {},
        position: { x: 0, y: 0, width: 12, height: 8 },
        isVisible: true
      }
    ],
    layout: 'full-width',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  restaurant_owner: {
    role: 'restaurant_owner',
    title: 'Restaurant Dashboard',
    description: 'Manage your restaurant operations',
    widgets: [
      {
        id: 'order-overview',
        type: 'order-overview',
        title: 'Today\'s Orders',
        data: {},
        position: { x: 0, y: 0, width: 8, height: 6 },
        isVisible: true
      },
      {
        id: 'revenue',
        type: 'revenue-chart',
        title: 'Revenue',
        data: {},
        position: { x: 8, y: 0, width: 4, height: 6 },
        isVisible: true
      }
    ],
    layout: 'grid',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  delivery_rider: {
    role: 'delivery_rider',
    title: 'Driver Dashboard',
    description: 'Manage your deliveries and earnings',
    widgets: [
      {
        id: 'active-deliveries',
        type: 'delivery-list',
        title: 'Active Deliveries',
        data: {},
        position: { x: 0, y: 0, width: 8, height: 6 },
        isVisible: true
      },
      {
        id: 'earnings-today',
        type: 'earnings',
        title: 'Today\'s Earnings',
        data: {},
        position: { x: 8, y: 0, width: 4, height: 6 },
        isVisible: true
      }
    ],
    layout: 'grid',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
};

// Default UI configurations for each role
const defaultUIConfigurations: { [role: string]: Omit<UIConfiguration, 'id'> } = {
  admin: {
    role: 'admin',
    theme: {
      primaryColor: '#3b82f6',
      secondaryColor: '#1e40af',
      accentColor: '#f59e0b',
      backgroundColor: '#f8fafc'
    },
    branding: {
      logo: '/vector---0.svg',
      title: 'Grubz',
      subtitle: 'Admin Portal'
    },
    features: {
      darkMode: true,
      notifications: true,
      analytics: true,
      userManagement: true,
      contentEditor: true
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  customer: {
    role: 'customer',
    theme: {
      primaryColor: '#dd3333',
      secondaryColor: '#c52e2e',
      accentColor: '#f59e0b',
      backgroundColor: '#ffffff'
    },
    branding: {
      logo: '/vector---0.svg',
      title: 'Grubz',
      subtitle: 'Grubber Portal'
    },
    features: {
      darkMode: false,
      notifications: true,
      orderTracking: true,
      favorites: true,
      partnerApplication: true
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  restaurant_owner: {
    role: 'restaurant_owner',
    theme: {
      primaryColor: '#704ce5',
      secondaryColor: '#5a3bc4',
      accentColor: '#f59e0b',
      backgroundColor: '#ffffff'
    },
    branding: {
      logo: '/vector---0.svg',
      title: 'Grubz',
      subtitle: 'Restaurant Portal'
    },
    features: {
      darkMode: false,
      notifications: true,
      menuManagement: true,
      orderManagement: true,
      analytics: true
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  delivery_rider: {
    role: 'delivery_rider',
    theme: {
      primaryColor: '#10b981',
      secondaryColor: '#059669',
      accentColor: '#f59e0b',
      backgroundColor: '#ffffff'
    },
    branding: {
      logo: '/vector---0.svg',
      title: 'Grubz',
      subtitle: 'Driver Portal'
    },
    features: {
      darkMode: false,
      notifications: true,
      gpsTracking: true,
      earningsTracking: true,
      deliveryHistory: true
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
};

export const initializeMenuData = async (): Promise<void> => {
  try {
    console.log('🔄 Initializing default menu data...');

    // Initialize navigation menus
    console.log('📋 Creating navigation menus...');
    for (const menu of defaultNavigationMenus) {
      await MenuService.createNavigationMenu(menu);
    }

    // Initialize dashboard content
    console.log('📊 Creating dashboard content...');
    for (const [role, content] of Object.entries(defaultDashboardContent)) {
      await MenuService.updateDashboardContent(role, content);
    }

    // Initialize UI configurations
    console.log('🎨 Creating UI configurations...');
    for (const [role, config] of Object.entries(defaultUIConfigurations)) {
      await MenuService.updateUIConfiguration(role, config);
    }

    console.log('✅ Default menu data initialized successfully!');
  } catch (error) {
    console.error('❌ Error initializing menu data:', error);
    throw error;
  }
};
